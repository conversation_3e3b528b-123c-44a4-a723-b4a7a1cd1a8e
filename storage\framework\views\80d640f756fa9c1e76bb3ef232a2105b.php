<div class="p-6">
    <h2 class="text-xl font-bold mb-4">Task Manager</h2>

    <!--[if BLOCK]><![endif]--><?php if(session()->has('message')): ?>
        <div class="text-green-600 mb-2"><?php echo e(session('message')); ?></div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <form wire:submit.prevent="<?php echo e($isEditMode ? 'updateTask' : 'createTask'); ?>" class="mb-6">
        <input type="text" wire:model="title" placeholder="Task title" class="border rounded px-2 py-1 mr-2">
        <select wire:model="user_id" class="border rounded px-2 py-1 mr-2">
            <option value="">Assign to...</option>
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($user->id); ?>"><?php echo e($user->name); ?></option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </select>
        <input type="datetime-local" wire:model="deadline" class="border rounded px-2 py-1 mr-2" required>
        <!--[if BLOCK]><![endif]--><?php if($isEditMode): ?>
            <select wire:model="status" class="border rounded px-2 py-1 mr-2">
                <option value="Pending">Pending</option>
                <option value="In Progress">In Progress</option>
                <option value="Completed">Completed</option>
            </select>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <button class=" bg-blue-500 text-white px-4 py-1 rounded">
            <?php echo e($isEditMode ? 'Update' : 'Create'); ?>

        </button>
        <!--[if BLOCK]><![endif]--><?php if($isEditMode): ?>
            <button type="button" wire:click="resetForm" class="ml-2 text-gray-500 underline">Cancel</button>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </form>

    <table class="w-full border-collapse table-auto">
        <thead class="bg-gray-100">
            <tr>
                <th class="px-2 py-1">Title</th>
                <th class="px-2 py-1">User</th>
                <th class="px-2 py-1">Status</th>
                <th class="px-2 py-1">Deadline</th>
                <th class="px-2 py-1">Actions</th>
            </tr>
        </thead>
        <tbody>
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="border-t space-y-2">
                    <td class="px-2 py-1"><?php echo e($task->title); ?></td>
                    <td class="px-2 py-1"><?php echo e($task->user->name); ?></td>
                    <td class="px-2 py-1 rounded-md <?php echo e($task->status === 'Pending' ? 'bg-yellow-500' :
       ($task->status === 'In Progress' ? 'bg-gray-500 text-white' :
       ($task->status === 'Completed' ? 'bg-green-500' : '') )); ?>"><?php echo e($task->status); ?></td>
                    <td class="px-2 py-1"><?php echo e(\Carbon\Carbon::parse($task->deadline)->toDayDateTimeString() ?? null); ?></td>
                    <td class="px-2 py-1 space-x-2">
                        <button wire:click="editTask(<?php echo e($task->id); ?>)" class="text-blue-600">Edit</button>
                        <button wire:click="deleteTask(<?php echo e($task->id); ?>)" class="text-red-600" onclick="return confirm('Sure?')">Delete</button>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </tbody>
    </table>
</div><?php /**PATH /var/www/html/resources/views/livewire/admin/task-manager.blade.php ENDPATH**/ ?>