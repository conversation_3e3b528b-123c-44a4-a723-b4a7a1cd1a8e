<div class="p-6">

    <h2 class="text-2xl font-bold mb-4">Manage Users</h2>

    <!--[if BLOCK]><![endif]--><?php if(session()->has('message')): ?>
        <div x-data="{ show: true }"
        x-init="setTimeout(() => show = false, 3000)"
        x-show="show"
        class="text-green-600 mb-4 transition-all duration-500 ease-in-out""><?php echo e(session('message')); ?></div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <form wire:submit.prevent="<?php echo e($isEditMode ? 'updateUser' : 'createUser'); ?>" class="mb-6 space-y-2">
        <input type="text" wire:model="name" placeholder="Name" class="border rounded px-2 py-1 w-full">
        <input type="email" wire:model="email" placeholder="Email" class="border rounded px-2 py-1 w-full">
        <!--[if BLOCK]><![endif]--><?php if(!$isEditMode): ?>
            <input type="password" wire:model="password" placeholder="Password" class="border rounded px-2 py-1 w-full">
            <input type="password" wire:model="password_confirmation" placeholder="Confirm Password" class="border rounded px-2 py-1 w-full">
        <?php else: ?>
            <input type="password" wire:model="password" placeholder="New Password (optional)" class="border rounded px-2 py-1 w-full">
            <input type="password" wire:model="password_confirmation" placeholder="Confirm Password" class="border rounded px-2 py-1 w-full">
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <select wire:model="role" class="border rounded px-2 py-1 w-full">
            <option value="">Select Role</option>
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $r): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($r->name); ?>"><?php echo e(ucfirst($r->name)); ?></option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </select>

        <div class="space-x-2">
            <button type="submit" class="bg-blue-600 text-white px-4 py-1 rounded">
                <?php echo e($isEditMode ? 'Update' : 'Create'); ?>

            </button>
            <!--[if BLOCK]><![endif]--><?php if($isEditMode): ?>
                <button type="button" wire:click="resetForm" class="text-gray-500 underline">Cancel</button>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </form>

    <table class="w-full border-collapse">
        <thead class="bg-gray-100">
            <tr>
                <th class="px-2 py-1">Name</th>
                <th class="px-2 py-1">Email</th>
                <th class="px-2 py-1">Role</th>
                <th class="px-2 py-1">Actions</th>
            </tr>
        </thead>
        <tbody>
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="border-t">
                    <td class="px-2 py-1"><?php echo e($user->name); ?></td>
                    <td class="px-2 py-1"><?php echo e($user->email); ?></td>
                    <td class="px-2 py-1"><?php echo e($user->roles->pluck('name')->first() ?? '-'); ?></td>
                    <td class="px-2 py-1 space-x-2">
                        <button wire:click="editUser(<?php echo e($user->id); ?>)" class="text-blue-600">Edit</button>
                        <button wire:click="deleteUser(<?php echo e($user->id); ?>)" class="text-red-600" onclick="return confirm('Are you sure?')">Delete</button>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </tbody>
    </table>
</div><?php /**PATH /var/www/html/resources/views/livewire/admin/user-manager.blade.php ENDPATH**/ ?>