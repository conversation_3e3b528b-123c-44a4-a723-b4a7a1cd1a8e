<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>Task</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" />

        <!-- Styles -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="antialiased font-sans">
        <div class="bg-gray-50 text-black/50 dark:bg-black dark:text-white/50">
            <img id="background" class="absolute -left-20 top-0 max-w-[877px]" src="https://laravel.com/assets/img/welcome/background.svg" />
            <div class="relative min-h-screen flex flex-col items-center justify-center selection:bg-[#FF2D20] selection:text-white">
                <div class="relative w-full max-w-2xl px-6 lg:max-w-7xl">
                    <header class="grid grid-cols-2 items-center gap-2 py-10 lg:grid-cols-3">
                        <div class="flex lg:justify-center lg:col-start-2">
                            <h2 class="font-bold text-lg text-black">TASK </h2>
                        </div>
                        @if (Route::has('login'))
                            <livewire:welcome.navigation />
                        @endif
                    </header>

                    <main>
                        <h1 class="text-4xl md:text-5xl font-extrabold mb-4 text-indigo-700">
                            Welcome to TaskFlow 🗂️
                        </h1>

                        <p class="text-lg mb-6 max-w-xl">
                            A simple and efficient way to manage tasks, assign users, and track progress. Designed for teams who value clarity and speed.
                        </p>
                    </main>


                    <footer class="py-16 text-center text-sm text-black dark:text-white/70">
                        Task manager by Elvis @ 2025
                    </footer>
                </div>
            </div>
        </div>
    </body>
</html>
